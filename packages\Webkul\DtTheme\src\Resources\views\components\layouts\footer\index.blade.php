{!! view_render_event('bagisto.shop.layout.footer.before') !!}

<!--
    The category repository is injected directly here because there is no way
    to retrieve it from the view composer, as this is an anonymous component.
-->
    <!-- We not using the category list. Hard coded below -->
<!-- @inject('themeCustomizationRepository', 'Webkul\Theme\Repositories\ThemeCustomizationRepository') -->

<!--
    This code needs to be refactored to reduce the amount of PHP in the Blade
    template as much as possible.
-->
    <!-- We not using the admin footer list. Hard coded below -->
<!-- @php
    $channel = core()->getCurrentChannel();

    $customization = $themeCustomizationRepository->findOneWhere([
        'type'       => 'footer_links',
        'status'     => 1,
        'theme_code' => $channel->theme,
        'channel_id' => $channel->id,
    ]);
@endphp -->

{!! view_render_event('bagisto.shop.layout.footer.newsletter_subscription.before') !!}

<!-- News Letter subscription -->
@if (core()->getConfigData('customer.settings.newsletter.subscription'))
    <div class="mt-20 bg-white px-8">
        <div class="max-w-[595px] mx-auto text-center">
            <!-- Main Heading -->
            <h2 class="dt-heading tracking-tight max-md:text-3xl max-sm:text-2xl">
                @lang('dt-theme::app.components.layouts.footer.newsletter-text')
            </h2>
            
            <!-- Subtitle -->
            <p class="dt-normalcase mb-8 max-md:text-base max-sm:text-sm">
                @lang('dt-theme::app.components.layouts.footer.subscribe-stay-touch')
            </p>
            
            <!-- Email Form -->
            <x-shop::form
                :action="route('shop.subscription.store')"
                class="max-w-lg mx-auto mb-4"
            >
                <div class="flex">
                    <x-shop::form.control-group.control
                        type="email"
                        class="flex-1 px-4 py-3 border border-gray-300 focus:outline-none focus:ring-2 focus:border-transparent max-sm:border-2 !mb-0"
                        name="email"
                        rules="required|email"
                        label="Email"
                        :aria-label="trans('shop::app.components.layouts.footer.email')"
                        placeholder="Enter your email address"
                    />

                    <button
                        type="submit"
                        class="button fontface-atten uppercase text-m button-orange text-white font-semibold px-8 py-3  max-sm:rounded-lg max-sm:px-6 max-sm:py-3"
                    >
                        @lang('dt-theme::app.components.layouts.footer.subscribe')
                    </button>
                </div>

                <x-shop::form.control-group.error control-name="email" class="mt-2" />
            </x-shop::form>
            
            <!-- Privacy Notice -->
            <p class="text-xs text-gray-500 mt-1 mx-auto">
                 @lang('dt-theme::app.components.layouts.footer.privacy')
            </p>
        </div>
    </div>
@endif

{!! view_render_event('bagisto.shop.layout.footer.newsletter_subscription.after') !!}

<footer class="mt-9 bg-lightOrange max-sm:mt-10">
<!-- Main container with grid layout -->
        <div class="flex flex-col md:flex-row justify-between gap-8 px-10 p-8 lg:gap-12">
            
            <!-- Logo and Social Section -->
            <div class="md:col-span-1">
                <div class="mb-6">
        <a
            href="{{ route('shop.home.index') }}"
            aria-label="@lang('shop::app.components.layouts.header.desktop.bottom.bagisto')"
        >
            <img
                src="{{ core()->getCurrentChannel()->logo_url ?? bagisto_asset('images/logo.svg') }}"
                width="131"
                height="29"
                alt="{{ config('app.name') }}"
            >
        </a>
                </div>
                
                <div class="mb-4">
                    <h3 class="font-atten text-neutral-800 font-bold text-m mb-4">GET SOCIAL</h3>
                    <div class="flex space-x-3">
                        <a href="#" class="text-gray-700 hover:text-gray-900">
                            <svg
                                class="w-5 h-5 fill-current"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                                aria-label="Facebook"
                            >
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-700 hover:text-gray-900">
                            <svg
                                class="w-5 h-5 fill-current"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                                aria-label="Instagram"
                            >
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-700 hover:text-gray-900">
                            <svg
                                class="w-5 h-5 fill-current"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                                aria-label="Pinterest"
                            >
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-12.013C24.007 5.367 18.641.001 12.017.001z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-700 hover:text-gray-900">
                            <svg
                                class="w-5 h-5 fill-current"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                                aria-label="YouTube"
                            >
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Collections Column -->
            <div class="md:col-span-1">
                <h3 class="font-atten text-neutral-800 font-bold text-m mb-4">COLLECTIONS</h3>
                <div class="flex gap-8">
                    <ul class="space-y-3">
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">New In</a></li>
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Sale</a></li>
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Gifts</a></li>
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Jewellery</a></li>
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Home & Garden</a></li>
                    </ul>
                    
                    <!-- Second sub-column -->
                    <ul class="space-y-3">
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Art & Prints</a></li>
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Accessories</a></li>
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Wellbeing</a></li>
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Food & Drink</a></li>
                        <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Staff Picks</a></li>
                    </ul>
                </div>
            </div>

            <!-- Shopping With Us Column -->
            <div class="md:col-span-1">
                <h3 class="font-atten text-neutral-800 font-bold text-m mb-4">SHOPPING WITH US</h3>
                <ul class="space-y-3">
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Delivery information</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Returns and cancellations</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">My Account</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">My Wishlist</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">My Order</a></li>
                </ul>
            </div>

            <!-- Useful Stuff Column -->
            <div class="md:col-span-1">
                <h3 class="font-atten text-neutral-800 font-bold text-m mb-4">USEFUL STUFF</h3>
                <ul class="space-y-3">
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Our Story</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">News & Events</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Contact us</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Terms & Conditions</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Privacy Policy</a></li>
                </ul>
            </div>

            <!-- For Sellers Column -->
            <div class="md:col-span-1">
                <h3 class="font-atten text-neutral-800 font-bold text-m mb-4">FOR SELLERS</h3>
                <ul class="space-y-3">
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Why sell with us?</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Seller FAQs</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Apply to sell with us</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Seller terms and policies</a></li>
                    <li class="mb-1.5"><a href="#" class="text-neutral-800 hover:text-gray-900 font-bold">Seller privacy and cookies</a></li>
                </ul>
            </div>
        </div>


    <div class="flex justify-between footer-peach px-[60px] py-3.5 max-md:justify-center max-sm:px-5">
        {!! view_render_event('bagisto.shop.layout.footer.footer_text.before') !!}

        <p class="text-sm text-zinc-600 max-md:text-center">
            @lang('dt-theme::app.components.layouts.footer.footer-text', ['current_year'=> date('Y') ])
        </p>

        {!! view_render_event('bagisto.shop.layout.footer.footer_text.after') !!}
    </div>
</footer>

{!! view_render_event('bagisto.shop.layout.footer.after') !!}
