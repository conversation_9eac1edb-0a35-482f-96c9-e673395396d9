<v-products-grid
    src="{{ $src }}"
    title="{{ $title }}"
    navigation-link="{{ $navigationLink ?? '' }}"
>
    <x-shop::shimmer.products.grid :navigation-link="$navigationLink ?? false" />
</v-products-grid>

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-products-grid-template"
    >
        <div
            class="mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4 px-10"
            v-if="! isLoading && products.length"
        >
            <div class="flex justify-between">
                <h2 class="dt-heading max-md:text-2xl max-sm:text-xl">
                    @{{ title }}
                </h2>

                <div
                    class="secondary-button flex items-center gap-x-2.5 border-0 bg-transparent p-0 text-base font-medium text-navyBlue max-lg:hidden"
                    v-if="navigationLink"
                >
                    <a :href="navigationLink">
                        @lang('dt-theme::app.components.products.grid.view-all')

                        <span
                            class="icon-arrow-right text-2xl"
                            role="presentation"
                        ></span>
                    </a>
                </div>

                <div
                    class="secondary-button flex items-center gap-x-2.5 border-0 bg-transparent p-0 text-base font-medium text-navyBlue lg:hidden"
                    v-if="navigationLink"
                >
                    <a :href="navigationLink">
                        @lang('dt-theme::app.components.products.grid.view-all')
                    </a>
                </div>
            </div>

            <!-- Product Grid -->
            <div class="mt-10 grid grid-cols-4 gap-8 max-1180:grid-cols-3 max-1060:grid-cols-2 max-md:mt-5 max-md:justify-items-center max-md:gap-x-4 max-md:gap-y-5">
                <x-shop::products.card
                    ::mode="'grid'"
                    v-for="product in products"
                />
            </div>
        </div>

        <!-- Product Grid Shimmer -->
        <template v-if="isLoading">
            <x-shop::shimmer.products.grid :navigation-link="$navigationLink ?? false" />
        </template>
    </script>

    <script type="module">
        app.component('v-products-grid', {
            template: '#v-products-grid-template',

            props: [
                'src',
                'title',
                'navigationLink',
            ],

            data() {
                return {
                    isLoading: true,

                    products: [],
                };
            },

            mounted() {
                this.getProducts();
            },

            methods: {
                getProducts() {
                    this.$axios.get(this.src)
                        .then(response => {
                            this.isLoading = false;

                            this.products = response.data.data;
                        }).catch(error => {
                            console.log(error);
                        });
                },
            },
        });
    </script>
@endPushOnce
